"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { <PERSON>o } from "next/font/google";
import { useRouter } from "next/navigation";
import { Select } from "antd";
import { SyncOutlined } from "@ant-design/icons";
import { fetchUserStatistics } from "@/src/api/userStatistics.api";
import { UserStatistics } from "@/src/services/userStatistics.service";
import {
  getTotalProgressData,
  getProgressData,
} from "@/src/services/statisticsDataProcessor.service";
import { formatCategoryTimeHMS } from "@/src/utils/chartTimeFormat";
import { ProgressPeriodData } from "../statistics/statisticsData";
import CircularProgress from "../statistics/circularProgress";
import HalfCircleProgressChart from "./halfCircleProgressChart";
import { formatToK } from "@/src/utils/helperFunctions";
import Skeleton from "react-loading-skeleton";
import { HiInformationCircle } from "react-icons/hi";
import InfoModal from "../statistics/infoModal";
import { useAudioContext } from "@/src/context/audio.context";
import { useVideoContext } from "@/src/context/video.context";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface StatisticsDropdownProps {
  onClose: () => void;
}

// Cache interfaces
interface ProgressCache {
  [period: string]: {
    data: ProgressPeriodData;
    timestamp: number;
  };
}

interface UserStatsCache {
  data: UserStatistics;
  timestamp: number;
}

interface TotalProgressCache {
  data: { percentage: number; lecturesListened: number };
  timestamp: number;
}

const StatisticsDropdown: React.FC<StatisticsDropdownProps> = ({ onClose }) => {
  const router = useRouter();
  const [userStats, setUserStats] = useState<UserStatistics | null>(null);
  const [progressData, setProgressData] = useState<ProgressPeriodData | null>(
    null
  );
  const [totalProgress, setTotalProgress] = useState<{
    percentage: number;
    lecturesListened: number;
  } | null>(null);
  const [progressPeriod, setProgressPeriod] = useState("All time");
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Cache refs for storing data to avoid repeated API calls
  const progressCache = useRef<ProgressCache>({});
  const userStatsCache = useRef<UserStatsCache | null>(null);
  const totalProgressCache = useRef<TotalProgressCache | null>(null);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  const { syncStatistics: syncAudioStatistics } = useAudioContext();
  const { syncStatistics: syncVideoStatistics } = useVideoContext();

  // Check if cached data is still valid
  const isCacheValid = (timestamp: number) => {
    return Date.now() - timestamp < CACHE_DURATION;
  };

  // Load user statistics with caching
  const loadUserStatistics = async () => {
    try {
      // Check cache first
      if (
        userStatsCache.current &&
        isCacheValid(userStatsCache.current.timestamp)
      ) {
        setUserStats(userStatsCache.current.data);
        return;
      }

      const stats = await fetchUserStatistics();
      if (stats) {
        // Cache the data
        userStatsCache.current = {
          data: stats,
          timestamp: Date.now(),
        };
        setUserStats(stats);
      }
    } catch (error) {
      console.error("Error loading user statistics:", error);
    }
  };

  // Load total progress data with caching
  const loadTotalProgressData = async () => {
    try {
      // Check cache first
      if (
        totalProgressCache.current &&
        isCacheValid(totalProgressCache.current.timestamp)
      ) {
        setTotalProgress(totalProgressCache.current.data);
        return;
      }

      const totalProgressData = await getTotalProgressData();
      // Cache the data
      totalProgressCache.current = {
        data: totalProgressData,
        timestamp: Date.now(),
      };
      setTotalProgress(totalProgressData);
    } catch (error) {
      console.error("Error loading total progress data:", error);
    }
  };

  // Load progress data with caching
  const loadProgressData = async (period: string) => {
    try {
      // Check cache first
      const cachedData = progressCache.current[period];
      if (cachedData && isCacheValid(cachedData.timestamp)) {
        setProgressData(cachedData.data);
        return;
      }

      const data = await getProgressData(period);
      // Cache the data
      progressCache.current[period] = {
        data,
        timestamp: Date.now(),
      };
      setProgressData(data);
    } catch (error) {
      console.error("Error loading progress data:", error);
    }
  };

  // Background sync function
  const backgroundSync = async () => {
    setIsSyncing(true);
    try {
      // Reload all data in background and sync statistics
      await Promise.all([syncAudioStatistics(), syncVideoStatistics()]);
      await Promise.all([
        loadUserStatistics(),
        loadTotalProgressData(),
        loadProgressData(progressPeriod),
      ]);
    } catch (error) {
      console.error("Error during background sync:", error);
    } finally {
      setIsSyncing(false);
    }
  };

  useEffect(() => {
    const loadStatisticsData = async () => {
      try {
        setIsLoading(true);

        // Load all data (will use cache if available)
        await Promise.all([
          loadUserStatistics(),
          loadTotalProgressData(),
          loadProgressData(progressPeriod),
        ]);

        // Start background sync after initial load
        setTimeout(() => {
          backgroundSync();
        }, 1000);
      } catch (error) {
        console.error("Error loading statistics data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStatisticsData();
  }, []);

  // Handle progress period change
  useEffect(() => {
    if (!isLoading) {
      loadProgressData(progressPeriod);
    }
  }, [progressPeriod]);

  const handleProgressPeriodChange = (period: string) => {
    setProgressPeriod(period);
    // Data will be loaded by the useEffect that watches progressPeriod
  };

  const handleViewStatistics = () => {
    router.push("/statistics");
    onClose();
  };

  if (isLoading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-lg p-4 md:p-6 w-[90vw] md:w-[80vw] lg:w-[750px] ${roboto.className}`}
      >
        <div className="flex flex-col md:flex-row gap-4 md:gap-6">
          {/* Cards Section Skeleton */}
          <div className="flex flex-row md:flex-col gap-6 w-full md:w-[200px]">
            {/* Streak Card Skeleton */}
            <div className="w-[50%] md:w-full">
              <Skeleton
                className="flex flex-col bg-gray-100 rounded-xl p-3 md:p-4 items-center justify-center md:h-1/2 w-1/2 md:w-full"
                height={180}
                borderRadius={10}
              />
            </div>

            {/* Total Progress Card Skeleton */}
            <div className="w-[50%] md:w-full">
              <Skeleton
                className="flex flex-col bg-gray-100 rounded-xl p-3 md:p-4 items-center justify-center md:h-1/2 w-1/2 md:w-full"
                height={180}
                borderRadius={10}
              />
            </div>
          </div>

          {/* Main Content Skeleton */}
          <div className="w-full h-[350px] md:h-[385px] md:w-[calc(100%-200px)]">
            <Skeleton
              borderRadius={10}
              className="w-full h-full flex-1 bg-gray-100 rounded-xl p-4"
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-lg shadow-lg p-4 md:p-6 w-[90vw] md:w-[80vw] lg:w-[750px] ${roboto.className}`}
    >
      {/* Responsive Layout */}
      <div className="flex flex-col md:flex-row gap-4 md:gap-6">
        {/* Cards Section - Responsive */}
        <div className="flex flex-row md:flex-col md:justify-center gap-4 md:gap-6 w-full md:w-[200px]">
          {/* Streak Card */}
          <div
            className="flex flex-col bg-white rounded-xl p-3 md:p-4 items-center justify-center md:h-[200px] w-1/2 md:w-full"
            style={{
              boxShadow: "0px 2px 8px 0px rgba(0, 0, 0, 0.16)",
            }}
          >
            <div className="flex items-center justify-between w-full mb-2">
              <span className="text-base md:text-xl font-medium text-[#343A40]">
                Streak
              </span>
              <Image
                src="/images/navbar/statistics/fire.svg"
                alt="Fire"
                width={20}
                height={20}
                className="w-4 h-4 md:w-6 md:h-6"
              />
            </div>
            <div className="flex flex-col justify-center items-center md:h-[calc(100%-32px)]">
              <div className="text-base text-[#9CA3AF] border-[#F5F5F5] border-[1px] p-1 md:p-2 rounded-full mb-1">
                <Image
                  src="/images/ui/flame.svg"
                  alt="Fire"
                  width={20}
                  height={20}
                  className="w-[40px] h-[40px] md:w-[56px] md:h-[56px] flex justify-center items-center"
                />
              </div>
              <div className="text-center">
                <div className="text-lg md:text-xl font-semibold text-[#343A40] mb-1">
                  {userStats ? userStats.currentStreak : 0}{" "}
                  {userStats
                    ? userStats.currentStreak > 1
                      ? "days"
                      : "day"
                    : "day"}
                </div>
              </div>
              <div className="text-sm md:text-md text-[#9CA3AF] text-center">
                {`Best Streak ${
                  userStats ? formatToK(userStats.bestStreak) : 0
                }`}
              </div>
            </div>
          </div>

          {/* Progress Card */}
          <div
            className="flex-1 md:flex-none bg-white rounded-xl p-3 md:p-4 flex flex-col items-center justify-center md:h-[200px] w-1/2 md:w-full"
            style={{
              boxShadow: "0px 2px 8px 0px rgba(0, 0, 0, 0.16)",
            }}
          >
            <div className="flex items-center justify-between w-full mb-2">
              <span className="text-base md:text-xl font-medium text-[#343A40]">
                Progress
              </span>
              <Image
                src="/images/navbar/statistics/upgrade.svg"
                alt="Progress"
                width={20}
                height={20}
                className="w-4 h-4 md:w-6 md:h-6"
              />
            </div>
            <div className="flex flex-col justify-center items-center gap-1 md:gap-2 flex-1">
              <div className="w-12 h-12 md:w-full md:h-auto">
                <div className="md:hidden">
                  <HalfCircleProgressChart
                    percentage={
                      totalProgress ? Math.round(totalProgress.percentage) : 0
                    }
                    size={130}
                    showPercentage={false}
                  />
                </div>
                <div className="hidden md:block">
                  <HalfCircleProgressChart
                    percentage={
                      totalProgress ? Math.round(totalProgress.percentage) : 0
                    }
                    size={180}
                    showPercentage={false}
                  />
                </div>
              </div>
              <div className="text-center -mt-2 md:-mt-10">
                <div className="text-lg md:text-xl font-semibold text-[#343A40]">
                  {totalProgress
                    ? `${Math.round(totalProgress.percentage)}%`
                    : "0%"}
                </div>
              </div>
              <div className="text-sm md:text-md text-[#9CA3AF] text-center">
                {totalProgress
                  ? `${totalProgress.lecturesListened} lectures so far`
                  : "0 lectures so far"}
              </div>
            </div>
          </div>
        </div>

        {/* Progress Since You Joined - Responsive */}
        <div className="flex-1 w-full">
          <div className="flex items-center justify-between mb-4">
            {/* <div className="flex items-center gap-2">
              <HiInformationCircle className="text-[#9CA3AF] text-[14px] md:text-[24px] cursor-pointer" onClick={() => setIsModalOpen(true)}/>
              <h3 className="text-base md:text-lg font-medium md:font-semibold text-[#343A40]">
                Progress since you joined
              </h3>
            </div>
            <div className="flex items-center gap-2">
              {isSyncing && (
                <SyncOutlined
                  spin
                  className="text-[#9CA3AF] text-xs md:text-sm"
                  title="Syncing data..."
                />
              )}
              <Select
                value={progressPeriod}
                onChange={handleProgressPeriodChange}
                className={`w-[100px] md:w-[130px] custome-selector ${roboto.className}`}
                size="small"
                options={[
                  { value: "All time", label: "All time" },
                  { value: "Last year", label: "Last year" },
                  {
                    value: "Last month",
                    label: "Last month",
                  },
                  { value: "Last week", label: "Last week" },
                ]}
                onDropdownVisibleChange={(value) => setIsDropdownOpen(value)}
                suffixIcon={
                  <Image
                    src="/images/helperComponents/arrow.svg"
                    width={12}
                    height={12}
                    alt=""
                    className={`w-5 h-5 mt-3 cursor-pointer transform transition-transform duration-300 ${
                      isDropdownOpen ? "rotate-180" : "rotate-0"
                    }`}
                  />
                }
              />
            </div> */}
            <div className="flex items-center min-w-0 gap-2 flex-1 overflow-hidden">
              <HiInformationCircle
                className="text-[#9CA3AF] text-[16px] sm:text-[18px] md:text-[18px] lg:text-[24px] flex-shrink-0 cursor-pointer"
                onClick={() => setIsModalOpen(true)}
              />
              {/* <span className="text-[14px] md:text-[18px] lg:text-[22px] font-[600] text-[#343A40]">
                  <div className="marquee w-[150px] sm:w-[210px]">
                    <span className="marquee-content">
                      Progress since you joined:
                    </span>
                  </div>
                </span> */}
              {/* Marquee for small devices <600px */}
              <div className="block sm:hidden text-[16px] font-[600] text-[#343A40] w-[150px]">
                <div className="marquee">
                  <span className="marquee-content">
                    Progress since you joined:
                  </span>
                </div>
              </div>

              {/* Static text for sm+ devices */}
              <div className="hidden sm:block md:text-[16px] lg:text-[18px] font-[600] text-[#343A40] truncate">
                Progress since you joined:
              </div>
            </div>
            <Select
              value={progressPeriod}
              onChange={handleProgressPeriodChange}
              className={`w-[90px] sm:w-[110px] lg:w-[130px] text-[12px] sm:text-[12px] lg:text-[16px] custome-selector ${roboto.className}`}
              popupClassName="custom-select-dropdown"
              size="small"
              options={[
                { value: "All time", label: "All time" },
                { value: "Last year", label: "Last year" },
                {
                  value: "Last month",
                  label: "Last month",
                },
                { value: "Last week", label: "Last week" },
              ]}
              onDropdownVisibleChange={(value) => setIsDropdownOpen(value)}
              suffixIcon={
                <Image
                  src="/images/helperComponents/arrow.svg"
                  width={12}
                  height={12}
                  alt=""
                  className={`w-5 h-5 sm:mt-3 mt-1 cursor-pointer transform transition-transform duration-300 ${
                    isDropdownOpen ? "rotate-180" : "rotate-0"
                  }`}
                />
              }
            />
          </div>

          {/* Circular Progress Chart - Responsive */}
          <div className="flex flex-row justify-center items-center relative">
            <div className="text-start mb-2 md:mb-0 md:z-10 top-0 left-0 absolute">
              <div className="text-lg md:text-2xl font-semibold text-[#343A40]">
                {progressData?.totalListened || "0h 0m"}
              </div>
              <div className="text-xs md:text-sm text-[#9CA3AF]">listened</div>
            </div>
            <div className="relative md:h-[260px] flex items-center justify-center">
              <div className="md:hidden">
                <CircularProgress
                  categories={progressData?.categories || []}
                  totalListened={progressData?.totalListened || "0h 0m"}
                  size={120}
                  scale={0.8}
                />
              </div>
              <div className="hidden md:block">
                <CircularProgress
                  categories={progressData?.categories || []}
                  totalListened={progressData?.totalListened || "0h 0m"}
                  size={200}
                  scale={0.8}
                />
              </div>
            </div>
          </div>

          {/* Category Legend - Responsive */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 md:gap-4 text-xs md:text-sm mb-4 md:mb-0">
            {progressData?.categories.map((category) => {
              const timeDisplay = category.seconds
                ? formatCategoryTimeHMS(category.seconds)
                    .replace(/^0h\s*/, "")
                    .replace(/^0m\s*/, "") || "0h"
                : `${category.hours}h`;

              return (
                <div key={category.name} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{
                      backgroundColor: category.color,
                    }}
                  ></div>
                  <div className="w-[calc(100%-20px)] flex flex-col">
                    <span className="text-[#9CA3AF] text-xs uppercase">
                      {category.name}
                    </span>
                    <div className="text-[14px] md:text-[14px] font-[600] text-[#343A40] overflow-hidden whitespace-nowrap">
                      <div
                        className="inline-block animate-marquee"
                        // style={{
                        //     animation:
                        //         "marquee 3s linear infinite",
                        // }}
                      >
                        {timeDisplay}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* View Statistics Link - Responsive */}
          <div className="text-center md:text-right mt-4 md:mt-6">
            <button
              type="button"
              onClick={handleViewStatistics}
              className="text-[#F97316] font-medium text-sm hover:text-[#EA580C] transition-colors"
            >
              View statistics ≫
            </button>
          </div>
        </div>
      </div>
      <InfoModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </div>
  );
};

export default StatisticsDropdown;
