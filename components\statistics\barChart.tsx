// "use client";
// import React from "react";
// import dynamic from "next/dynamic";
// import { formatCategoryTimeHMS } from "@/src/utils/chartTimeFormat";
// import { Zoom } from "@mui/material";

// // Dynamically import ApexCharts to avoid SSR issues
// const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

// interface ChartDataItem {
//   day: string;
//   date: string | number;
//   hours: number;
// }

// interface BarChartProps {
//   data: ChartDataItem[];
//   height?: number;
//   activeTab?: string;
// }

// const BarChart: React.FC<BarChartProps> = ({
//   data,
//   height = 200,
//   activeTab = "Week",
// }) => {
//   // Get chart axis configuration with proper time formatting
//   const maxHours = Math.max(...data.map((item) => item.hours));

//   // Determine if we should use minutes or hours based on max value
//   const shouldUseMinutes = maxHours < 1;

//   const getMaxValue = () => {
//     if (shouldUseMinutes) {
//       const maxMinutes = Math.ceil(maxHours * 60);
//       return Math.ceil(maxMinutes / 10) * 10; // Round to nearest 10 minutes
//     } else {
//       return Math.ceil(maxHours); // Round up to nearest hour
//     }
//   };

//   const barChartOptions = {
//     chart: {
//       type: "bar" as const,
//       height: height,
//       toolbar: { show: false },
//       background: "transparent",
//       pan: {
//       enabled: true,
//       type: 'x',
//     },
//     zoom: {
//     enabled: true
//   }
//     },
//     plotOptions: {
//       bar: {
//         borderRadius: 4,
//         columnWidth: "20px",
//         colors: {
//           ranges: [
//             {
//               from: 0,
//               to: 100,
//               color: "#F9DFCA",
//             },
//           ],
//         },
//       },
//     },
//     states: {
//       hover: {
//         filter: {
//           type: "none",
//         },
//       },
//       active: {
//         filter: {
//           type: "none",
//         },
//       },
//     },
//     dataLabels: { enabled: false },
//     stroke: { show: false },
//     xaxis: {
//       categories: data.map((item) => item),
//       labels: {
//         formatter: function (value: any, timestamp?: number, opts?: any) {
//           const item = value as ChartDataItem;
//           // For Week view, show day name and date
//           if (activeTab === "Week") {
//             return [String(item.date), item?.day?.toUpperCase()];
//           }
//           // For Month view, show only date
//           else if (activeTab === "Month") {
//             return [String(item.date)];
//           }
//           // For Year view, show month name
//           else {
//             return [item?.day?.toUpperCase()];
//           }
//         },
//         style: {
//           colors: "#959698",
//           fontSize: "14px",
//           fontFamily: "'Poppins', sans-serif",
//         },
//         rotate: 0,
//         trim: false,
//         hideOverlappingLabels: false,
//       },
//       axisBorder: { show: true },
//       axisTicks: { show: false },
//       scrollbar: {
//       enabled: true,
//       height: 8,
//       track: {
//         background: '#f1f1f1',
//         strokeWidth: 1,
//         stroke: '#e0e0e0',
//         borderRadius: 4
//       },
//       thumb: {
//         background: '#c1c1c1',
//         borderRadius: 4
//       }
//     }
//     },
//     yaxis: {
//       opposite: true,
//       min: 0, // Always start Y-axis from 0 to prevent negative values
//       max: shouldUseMinutes ? getMaxValue() / 60 : getMaxValue(),
//       labels: {
//         formatter: shouldUseMinutes
//           ? (value: number) => `${Math.round(value * 60)}m`
//           : (value: number) =>
//               value % 1 === 0 ? `${value}h` : `${value.toFixed(1)}h`,
//         style: {
//           colors: "#959698",
//           fontSize: "14px",
//           fontFamily: "'Poppins', sans-serif",
//         },
//       },
//     },
//     grid: {
//       borderColor: "#F3EFED",
//       strokeDashArray: 0,
//       xaxis: { lines: { show: false } },
//       yaxis: { lines: { show: true } },
//     },
//     tooltip: {
//       custom: function ({ dataPointIndex }: any) {
//         const item = data[dataPointIndex];

//         // Convert hours to seconds for h m s formatting
//         const totalSeconds = Math.round(item.hours * 3600);
//         const hours = Math.floor(totalSeconds / 3600);
//         const minutes = Math.floor((totalSeconds % 3600) / 60);
//         const seconds = Math.floor(totalSeconds % 60);

//         // Format time in h m s format
//         const parts: string[] = [];
//         if (hours > 0) parts.push(`${hours}h`);
//         if (minutes > 0) parts.push(`${minutes}m`);
//         if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`);
//         const timeDisplay = parts.join(" ");

//         return `<div style="
//                     color: #F97316;
//                     padding: 8px 12px;
//                     border-radius: 8px;
//                     font-family: 'Poppins', sans-serif;
//                     font-size: 16px;
//                     font-weight: 600;
//                     box-shadow: 0 4px 12px rgba(0,0,0,0.15);
//                     position: relative;
//                 ">
//                     ${timeDisplay}
//                     <div style="
//                         position: absolute;
//                         bottom: -6px;
//                         left: 50%;
//                         transform: translateX(-50%);
//                         width: 0;
//                         height: 0;
//                         border-left: 6px solid transparent;
//                         border-right: 6px solid transparent;
//                         border-top: 6px solid #F97316;
//                     "></div>
//                 </div>`;
//       },
//     },
//     fill: {
//       colors: ["#F9DFCA"],
//     },
//   };

//   const barChartSeries = [
//     {
//       name: "Hours",
//       data: data.map((item) => ({
//         x: item.date.toString(),
//         y: Math.max(0, item.hours || 0), // Ensure non-negative values
//         fillColor: "#F9DFCA",
//       })),
//     },
//   ];

//   return (
//     <div className="w-full">
//       <style jsx global>{`
//         .apexcharts-bar-area:hover {
//           fill: #f97316 !important;
//         }
//         .apexcharts-series path:hover {
//           fill: #f97316 !important;
//         }
//         .apexcharts-series rect:hover {
//           fill: #f97316 !important;
//         }

//         @media (max-width: 768px) {
//           .apexcharts-scrollbar {
//             display: block !important;
//           }
//           .apexcharts-scrollbar-thumb {
//             background: #f97316 !important;
//           }
//         }
//       `}</style>
//       {typeof window !== "undefined" && (
//         <Chart
//           options={barChartOptions}
//           series={barChartSeries}
//           type="bar"
//           height={height}
//         />
//       )}
//     </div>
//   );

// };

// export default BarChart;

///////////////////////////////////////////////

"use client";
import React, { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import { formatCategoryTimeHMS } from "@/src/utils/chartTimeFormat";
import { Zoom } from "@mui/material";

const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface ChartDataItem {
  day: string;
  date: string | number;
  hours: number;
}

interface BarChartProps {
  data: ChartDataItem[];
  height?: number;
  activeTab?: string;
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  height = 200,
  activeTab = "Week",
}) => {
  const maxHours = Math.max(...data.map((item) => item.hours));
  const shouldUseMinutes = maxHours < 1;
  const chartRef = useRef<HTMLDivElement>(null);
  const [yTicks, setYTicks] = useState<string[]>([]);
  const observerRef = useRef<MutationObserver | null>(null);
  const [gridTopOffset, setGridTopOffset] = useState(0);

  useEffect(() => {
    if (typeof window === "undefined" || window.innerWidth >= 768) return;

    let observer: MutationObserver | null = null;
    let interval: NodeJS.Timeout | null = null;
    let debounceTimeout: NodeJS.Timeout | null = null;

    const extractTicks = () => {
      const yLabels = document.querySelectorAll(".apexcharts-yaxis-label");
      if (yLabels.length === 0) return;

      const ticks = Array.from(yLabels).map((el) => {
        const text = (el as HTMLElement).textContent?.trim() || "";
        const match = text.match(/(\d+\s*[hms])/i);
        return match ? match[0] : text;
      });

      const hasAllTicks = ticks.length && ticks.every((tick) => !!tick);
      if (hasAllTicks) {
        setYTicks((prev) =>
          ticks.join(",") !== prev.join(",") ? ticks.reverse() : prev
        );
      }
    };

    const debounceExtract = () => {
      if (debounceTimeout) clearTimeout(debounceTimeout);
      debounceTimeout = setTimeout(() => {
        extractTicks();
      }, 200); // Wait for DOM to settle
    };

    const chart = document.querySelector(".apexcharts-yaxis");
    if (chart) {
      observer = new MutationObserver(() => {
        debounceExtract(); // Use debounce to avoid rapid invalid updates
      });
      observer.observe(chart, { childList: true, subtree: true });
    }

    // Initial extraction loop (wait for Apex to finish first render)
    let attempts = 0;
    interval = setInterval(() => {
      if (attempts > 10) {
        clearInterval(interval!);
        return;
      }
      extractTicks();
      attempts++;
    }, 200);

    return () => {
      if (interval) clearInterval(interval);
      if (observer) observer.disconnect();
      if (debounceTimeout) clearTimeout(debounceTimeout);
    };
  }, [activeTab, data, height]);

  const [plotHeight, setPlotHeight] = useState(0);

  const getMaxValue = () => {
    if (shouldUseMinutes) {
      const maxMinutes = Math.ceil(maxHours * 60);
      return Math.ceil(maxMinutes / 10) * 10;
    } else {
      return Math.ceil(maxHours);
    }
  };

  const barChartOptions = {
    chart: {
      type: "bar" as const,
      height: height,
      toolbar: { show: false },
      background: "transparent",
      pan: {
        enabled: true,
        type: "x",
      },
      animations: { enabled: false },
      // events: {
      //   mounted: () => {
      //     if (window.innerWidth >= 768) return;

      //     setTimeout(() => {
      //       const yLabels = document.querySelectorAll(
      //         ".apexcharts-yaxis-label"
      //       );
      //       const ticks = Array.from(yLabels).map(
      //         (el) => (el as HTMLElement).innerText
      //       );
      //       setYTicks(ticks.reverse()); // top to bottom
      //     }, 500);
      //   },
      // },
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "20px",
        colors: {
          ranges: [
            {
              from: 0,
              to: 100,
              color: "#F9DFCA",
            },
          ],
        },
      },
    },
    states: {
      hover: { filter: { type: "none" } },
      active: { filter: { type: "none" } },
    },
    dataLabels: { enabled: false },
    stroke: { show: false },
    xaxis: {
      categories: data.map((item) => item),
      labels: {
        show: true,
        formatter: function (value: any) {
          const item = value as ChartDataItem;
          if (activeTab === "Week") {
            return [String(item.date), item?.day?.toUpperCase()];
          } else if (activeTab === "Month") {
            return [String(item.date)];
          } else {
            return [item?.day?.toUpperCase()];
          }
        },
        style: {
          colors: "#959698",
          fontSize: "14px",
          fontFamily: "'Poppins', sans-serif",
        },
        rotate: 0,
        trim: false,
        hideOverlappingLabels: false,
      },
      axisBorder: { show: true },
      axisTicks: { show: false },
      scrollbar: {
        enabled: true,
        height: 8,
        track: {
          background: "#f1f1f1",
          strokeWidth: 1,
          stroke: "#e0e0e0",
          borderRadius: 4,
        },
        thumb: {
          background: "#c1c1c1",
          borderRadius: 4,
        },
      },
    },
    yaxis: {
      opposite: true,
      min: 0,
      max: shouldUseMinutes ? getMaxValue() / 60 : getMaxValue(),
      // tickAmount: tickAmount,
      labels: {
        formatter: shouldUseMinutes
          ? (value: number) => `${Math.round(value * 60)}m`
          : (value: number) =>
              value % 1 === 0 ? `${value}h` : `${value.toFixed(1)}h`,
        style: {
          colors: "#959698",
          fontSize: "14px",
          fontFamily: "'Poppins', sans-serif",
        },
      },
    },
    grid: {
      borderColor: "#F3EFED",
      strokeDashArray: 0,
      xaxis: { lines: { show: false } },
      yaxis: { lines: { show: true } },
    },
    tooltip: {
      custom: function ({ dataPointIndex }: any) {
        const item = data[dataPointIndex];
        const totalSeconds = Math.round(item.hours * 3600);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = Math.floor(totalSeconds % 60);
        const parts: string[] = [];
        if (hours > 0) parts.push(`${hours}h`);
        if (minutes > 0) parts.push(`${minutes}m`);
        if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`);
        const timeDisplay = parts.join(" ");
        return `<div style="
                    color: #F97316;
                    padding: 8px 12px;
                    border-radius: 8px;
                    font-family: 'Poppins', sans-serif;
                    font-size: 16px;
                    font-weight: 600;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    position: relative;
                ">
                    ${timeDisplay}
                    <div style="
                        position: absolute;
                        bottom: -6px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 0;
                        height: 0;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        border-top: 6px solid #F97316;
                    "></div>
                </div>`;
      },
    },
    fill: {
      colors: ["#F9DFCA"],
    },
  };

  const barChartSeries = [
    {
      name: "Hours",
      data: data.map((item) => ({
        x: item.date.toString(),
        y: Math.max(0, item.hours || 0),
        fillColor: "#F9DFCA",
      })),
    },
  ];

  useEffect(() => {
    const chartEl = chartRef.current;
    if (chartEl) {
      const plotRect = chartEl
        .querySelector(".apexcharts-inner")
        ?.getBoundingClientRect();
      const gridRect = chartEl
        .querySelector(".apexcharts-grid")
        ?.getBoundingClientRect();

      if (plotRect && gridRect) {
        setPlotHeight(gridRect.height); 
        setGridTopOffset(gridRect.top - plotRect.top);
      }
    }
  }, [activeTab, chartRef.current, barChartSeries]); 
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;

  return (
    <div className="w-full relative">
      {/* MOBILE VIEW: chart with fixed y-axis */}
      <div className="md:hidden flex relative">
        <div className="overflow-x-auto scrollbar-hide w-full" ref={chartRef}>
          <div
            // className="min-w-[750px]"
            style={{
              minWidth: `${data.length * 60}px`,
              // paddingRight: "40px", // space for custom y-axis
            }}
          >
            {/* {typeof window !== "undefined" && ( */}
            <Chart
              // options={{
              //   ...barChartOptions,
              //   yaxis: { ...barChartOptions.yaxis, labels: { show: false } },
              // }}
              options={barChartOptions}
              series={barChartSeries}
              type="bar"
              height={height}
            />
            {/* )} */}
          </div>
        </div>

        {isMobile && plotHeight && yTicks.length > 0 && (
          <div
            className="absolute right-0 z-50 pointer-events-none"
            style={{
              top: `${gridTopOffset + 15}px`,
              height: `${plotHeight + 15}px`,
              width: "30px",
              background: "white",
            }}
          >
            {yTicks.map((tick, index) => {
              const spacing = (plotHeight / (yTicks.length - 1)) * index;
              return (
                <div
                  key={index}
                  className="absolute right-0 left-0 flex items-center  text-[#959698] text-[14px] font-medium font-[Poppins]"
                  style={{
                    top: `${spacing}px`,
                    transform: "translateY(0%)",
                    background: "white",
                  }}
                >
                  <span>{tick}</span>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* DESKTOP VIEW: default chart */}
      <div className="hidden md:block">
        {typeof window !== "undefined" && (
          <Chart
            options={barChartOptions}
            series={barChartSeries}
            type="bar"
            height={height}
          />
        )}
      </div>

      <style jsx global>{`
        .apexcharts-bar-area:hover {
          fill: #f97316 !important;
        }
        .apexcharts-series path:hover {
          fill: #f97316 !important;
        }
        .apexcharts-series rect:hover {
          fill: #f97316 !important;
        }
        @media (max-width: 760px) {
          .apexcharts-yaxis {
            display: none !important;
            // opacity: 0 !important;
          }
        }
      `}</style>
    </div>
  );
};

export default BarChart;
