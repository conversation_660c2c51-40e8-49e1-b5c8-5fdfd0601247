"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useMap,
} from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/src/config/firebase.config";
import Image from "next/image";
import { Poppins } from "next/font/google";
import {
  FaChevronLeft,
  FaChevronRight,
  FaDownload,
  FaTimes,
} from "react-icons/fa";
import ItineraryCard from "./itineraryCard";
import {
  CenterMapOnSelected,
  formatDateRange,
} from "@/src/utils/helperFunctions";
import { fetchItinerary } from "@/src/api/itinerary.api";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const orangeIcon = new L.Icon({
  iconUrl: "/images/itinerary/marker.svg",
  iconSize: [40, 40],
  iconAnchor: [20, 20],
  popupAnchor: [0, -20],
});

const Itinerary = () => {
  const [itinerary, setItinerary] = useState<any[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedStopId, setSelectedStopId] = useState<string | null>(null);
  const mobileCardRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const desktopCardRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const mapRef = useRef<L.Map | null>(null);
  const mobileScrollRef = useRef<HTMLDivElement | null>(null);

  // useEffect(() => {
  //   const selectedCard = cardRefs.current[selectedStopId || ""];
  //   if (selectedCard) {
  //     selectedCard.scrollIntoView({
  //       behavior: "smooth",
  //       block: "nearest",
  //       inline: window.innerWidth < 768 ? "nearest" : "nearest",
  //     });
  //   }
  // }, [selectedStopId]);

  useEffect(() => {
    const isMobile = window.innerWidth < 768;
    const selectedCard = isMobile
      ? mobileCardRefs.current[selectedStopId || ""]
      : desktopCardRefs.current[selectedStopId || ""];

    if (selectedCard && selectedStopId) {
      if (isMobile && mobileScrollRef.current) {
        setTimeout(() => {
          selectedCard.scrollIntoView({
            behavior: "smooth",
            inline: "center",
            block: "nearest",
          });
        }, 50);
      } else {
        selectedCard.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "nearest",
        });
      }
    }
  }, [selectedStopId]);

  const handlePrev = () => {
    if (!selectedLocation) return;
    setSelectedImageIndex((prev) => Math.max(prev - 1, 0));
  };

  const handleNext = () => {
    if (!selectedLocation) return;
    setSelectedImageIndex((prev) =>
      Math.min(prev + 1, selectedLocation.images.length - 1)
    );
  };

  const handleClose = () => {
    setSelectedLocation(null);
    setSelectedImageIndex(0);
  };

  // useEffect(() => {
  //   const fetchData = async () => {
  //     const querySnapshot = await getDocs(collection(db, "itinerary"));
  //     const items = querySnapshot.docs.map((doc) => ({
  //       id: doc.id,
  //       ...doc.data(),
  //     }));
  //     setItinerary(items);
  //   };
  //   fetchData();
  // }, []);

  useEffect(() => {
    const loadItinerary = async () => {
      try {
        const futureItems = await fetchItinerary();
        setItinerary(futureItems);

        if (futureItems.length > 0) {
          setSelectedStopId(futureItems[0].id);
        }
      } catch (error) {
        console.error("Failed to load itinerary", error);
      }
    };

    loadItinerary();
  }, []);

  const polylinePoints = itinerary.map((item) => [
    item.venue.latitude,
    item.venue.longitude,
  ]);

  const selectedMarkerIcon = new L.DivIcon({
    // html: `<div class="pulse-marker"></div>`,
    html: `
    <div class="pulse-wrapper">
      <img src="/images/itinerary/marker.svg" class="marker-icon" />
    </div>
  `,
    className: "", // removes default Leaflet icon wrapper
    // iconSize: [20, 20],
    // iconAnchor: [20, 20],
    iconSize: [40, 40],
    iconAnchor: [20, 20],
  });

  return (
    <div className={`w-full h-full ${poppins.className} tracking-normal`}>
      {/* Header */}
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end px-4 md:py-3 border-b">
        <h1 className="text-[24px] md:text-[28px] font-[600] leading-8">
          Itinerary
        </h1>
      </div>

      {/* Map */}
      <div
        style={{
          width: "100%",
          height: "calc(100% - 84px)",
          position: "relative",
          zIndex: 0,
        }}
      >
        <MapContainer
          center={[30, 20]}
          zoom={2}
          scrollWheelZoom
          style={{ width: "100%", height: "100%" }}
        >
          <CenterMapOnSelected
            selectedStop={itinerary.find((stop) => stop.id === selectedStopId)}
          />
          <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />

          {/* Polyline connecting stops */}
          {polylinePoints.length > 1 && (
            <Polyline positions={polylinePoints} color="#003F6B" weight={3} />
          )}

          {/* Markers */}
          {itinerary.map((stop) => (
            <Marker
              key={stop.id}
              position={[stop.venue.latitude, stop.venue.longitude]}
              // icon={orangeIcon}
              icon={
                selectedStopId === stop.id ? selectedMarkerIcon : orangeIcon
              }
              eventHandlers={{
                click: () => setSelectedStopId(stop.id),
              }}
            >
              {selectedStopId === stop.id && (
                <Tooltip
                  permanent
                  direction="top"
                  offset={[0, -20]}
                  opacity={1}
                >
                  {formatDateRange(stop.period.startDate, stop.period.endDate)}
                </Tooltip>
              )}
              {/* <Tooltip permanent direction="top" offset={[0, -20]} opacity={1}>
                {formatDateRange(stop.period.startDate, stop.period.endDate)}
              </Tooltip> */}
            </Marker>
          ))}
        </MapContainer>

        {/* Sidebar with itinerary cards */}
        {/* <div className="absolute top-0 right-0 h-full w-[30%] z-[1000] p-4">
          <div className="flex flex-col overflow-y-auto max-h-full gap-6 scrollbar-hide cursor-pointer">
            {itinerary.map((stop) => (
              <div
                key={stop.id}
                ref={(el) => {
                  cardRefs.current[stop.id] = el;
                }}
                className="flex flex-col items-start gap-2"
                onClick={() => {
                  setSelectedStopId(stop.id);
                  setSelectedImageIndex(0);
                }}
              >
                
                <div
                  className={`text-[14px] font-normal text-black px-2 py-1 rounded-lg  ${
                    selectedStopId === stop.id
                      ? "bg-[#FFF4EB] border-2 border-[#FF710166] "
                      : "bg-white border border-white shadow"
                  }`}
                >
                  {formatDateRange(stop.period.startDate, stop.period.endDate)}
                </div>

                
                <ItineraryCard
                  stop={stop}
                  isSelected={selectedStopId === stop.id}
                  onClick={() => {
                    setSelectedStopId(stop.id);
                    setSelectedImageIndex(0);
                  }}
                  onZoomClick={() => {
                    setSelectedLocation(stop);
                    setSelectedImageIndex(0);
                  }}
                />
              </div>
            ))}
          </div>
        </div> */}

        <div className="md:hidden fixed bottom-0 left-0 w-full z-[1000] p-4">
          <div
            ref={mobileScrollRef}
            className="flex gap-4 overflow-x-auto scrollbar-hide max-w-full scroll-smooth"
            style={{ scrollSnapType: "x mandatory" }}
          >
            {itinerary.map((stop) => (
              <div
                key={stop.id}
                ref={(el) => {
                  mobileCardRefs.current[stop.id] = el;
                }}
                className="flex flex-col items-start gap-2 min-w-[270px] max-w-[280px] flex-shrink-0"
                style={{ scrollSnapAlign: "start" }}
                onClick={() => {
                  setSelectedStopId(stop.id);
                  setSelectedImageIndex(0);
                }}
              >
                {/* Date tag */}
                <div
                  className={`text-[14px] font-normal text-black px-2 py-1 rounded-lg ${
                    selectedStopId === stop.id
                      ? "bg-[#FFF4EB] border-2 border-[#FF710166]"
                      : "bg-white border border-white shadow"
                  }`}
                >
                  {formatDateRange(stop.period.startDate, stop.period.endDate)}
                </div>

                <ItineraryCard
                  stop={stop}
                  isSelected={selectedStopId === stop.id}
                  onClick={() => {
                    setSelectedStopId(stop.id);
                    setSelectedImageIndex(0);
                  }}
                  onZoomClick={() => {
                    setSelectedLocation(stop);
                    setSelectedImageIndex(0);
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        {/* DESKTOP: Your original right-side panel (unchanged) */}
        <div className="hidden md:block absolute top-0 right-0 h-full w-[30%] z-[1000] p-4">
          <div className="flex flex-col overflow-y-auto max-h-full gap-6 scrollbar-hide cursor-pointer">
            {itinerary.map((stop) => (
              <div
                key={stop.id}
                ref={(el) => {
                  desktopCardRefs.current[stop.id] = el;
                }}
                className="flex flex-col items-start gap-2"
                onClick={() => {
                  setSelectedStopId(stop.id);
                  setSelectedImageIndex(0);
                }}
              >
                <div
                  className={`text-[14px] font-normal text-black px-2 py-1 rounded-lg ${
                    selectedStopId === stop.id
                      ? "bg-[#FFF4EB] border-2 border-[#FF710166]"
                      : "bg-white border border-white shadow"
                  }`}
                >
                  {formatDateRange(stop.period.startDate, stop.period.endDate)}
                </div>

                <ItineraryCard
                  stop={stop}
                  isSelected={selectedStopId === stop.id}
                  onClick={() => {
                    setSelectedStopId(stop.id);
                    setSelectedImageIndex(0);
                  }}
                  onZoomClick={() => {
                    setSelectedLocation(stop);
                    setSelectedImageIndex(0);
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {selectedLocation && (
        <div
          className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm px-4"
          onClick={handleClose}
        >
          <div
            className="relative w-full max-w-3xl mx-auto px-2 sm:px-4 flex flex-col items-center gap-4"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Image & Nav Buttons Row */}
            <div className="relative w-full flex items-center justify-between">
              {/* Prev button */}
              {selectedLocation.images.length > 1 && (
                <button
                  onClick={handlePrev}
                  disabled={selectedImageIndex === 0}
                  className={`rounded-full sm:p-3 p-1 transition ${
                    selectedImageIndex === 0
                      ? "bg-secondary text-gray-400 cursor-not-allowed"
                      : "bg-white text-black hover:bg-opacity-80"
                  }`}
                >
                  <FaChevronLeft size={14} />
                </button>
              )}

              {/* Image */}
              <div className="w-full flex items-center justify-center max-h-[80vh] px-2 sm:px-6">
                <img
                  src={selectedLocation.images[selectedImageIndex]}
                  alt="Preview"
                  className="object-contain w-full max-h-[60vh] sm:max-h-[70vh] md:max-h-[60vh] rounded-lg"
                />
              </div>

              {/* Next button */}
              {selectedLocation.images.length > 1 && (
                <button
                  onClick={handleNext}
                  disabled={
                    selectedImageIndex === selectedLocation.images.length - 1
                  }
                  className={`rounded-full sm:p-3 p-1 transition ${
                    selectedImageIndex === selectedLocation.images.length - 1
                      ? "bg-secondary text-gray-400 cursor-not-allowed"
                      : "bg-white text-black hover:bg-opacity-80"
                  }`}
                >
                  <FaChevronRight size={14} />
                </button>
              )}
            </div>

            {/* Description */}
            <div className="mt-4 text-gray-100 font-normal text-[16px] text-center px-4">
              <p>{selectedLocation.description?.en}</p>
            </div>

            {/* Close button */}
            <button
              onClick={handleClose}
              className="mt-8 text-white bg-secondary bg-opacity-60 rounded-full p-3 hover:bg-opacity-80 transition"
            >
              <FaTimes size={14} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Itinerary;
