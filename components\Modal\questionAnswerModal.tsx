"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  fetchNextQuestion,
  updateUserAnswer,
} from "../../src/api/question-answer.api";
import { IoMdClose } from "react-icons/io";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

const QuestionPopup = () => {
  const [questions, setQuestions] = useState<any[]>([]);
  const [answers, setAnswers] = useState<any>({});
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState<any[]>([]);
  const [showPopup, setShowPopup] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // useEffect(() => {
  //   const alreadyShown = sessionStorage.getItem("questionPopupShown");
  //   if (alreadyShown) return;

  //   const loadData = async () => {
  //     const qs = await fetchQuestions();
  //     const ans = await fetchUserAnswers();

  //     setQuestions(qs);
  //     setAnswers(ans);

  //     const idx = qs.findIndex(q => !(q.id in ans));
  //     if (idx >= 0) {
  //       setCurrentIndex(idx);
  //       setShowPopup(true);
  //       sessionStorage.setItem("questionPopupShown", "true");
  //     }
  //   };

  //   loadData();
  // }, []);

  useEffect(() => {
    const currentPath = window.location.pathname;
    const lastPath = sessionStorage.getItem("lastPath");
    const alreadyShown = sessionStorage.getItem("questionPopupShown");

    //First: Compare paths before updating lastPath
    if (lastPath && lastPath !== currentPath) {
      // SPA route change → do not show popup
      sessionStorage.setItem("questionPopupShown", "true");
    }

    //Then: Always update lastPath for future comparisons
    sessionStorage.setItem("lastPath", currentPath);

    //Show popup only if not already shown
    if (alreadyShown === "true") return;

    // const loadData = async () => {
    //   const qs = await fetchQuestions();
    //   const ans = await fetchUserAnswers();

    //   setQuestions(qs);
    //   setAnswers(ans);

    //   const idx = qs.findIndex((q) => !(q.id in ans));
    //   if (idx >= 0) {
    //     setCurrentIndex(idx);
    //     setShowPopup(true);
    //     sessionStorage.setItem("questionPopupShown", "true");
    //   }
    // };

    const loadData = async () => {
      const nextQuestion = await fetchNextQuestion();
      if (nextQuestion) {
        setQuestions([nextQuestion]); // only one question now
        setCurrentIndex(0);
        setShowPopup(true);
        sessionStorage.setItem("questionPopupShown", "true");
      }
    };

    loadData();
  }, []);

  useEffect(() => {
    const handleBeforeUnload = () => {
      sessionStorage.setItem("questionPopupShown", "false");
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, []);

  if (!showPopup || !questions.length) return null;

  const q = questions[currentIndex];

  const isSelected = (optId: any) => selectedOptions.includes(optId);

  const toggleOption = (optId: any) => {
    if (q.isMultipleSelection) {
      setSelectedOptions((prev) =>
        prev.includes(optId)
          ? prev.filter((id) => id !== optId)
          : [...prev, optId]
      );
    } else {
      setSelectedOptions([optId]);
    }
  };

  // const proceedToNext = (newAnswers: any) => {
  //   const nextIdx = questions.findIndex(
  //     (question, i) => i > currentIndex && !newAnswers[question.id]
  //   );
  //   if (nextIdx >= 0) {
  //     setSelectedOptions([]);
  //     setCurrentIndex(nextIdx);
  //   } else {
  //     setShowPopup(false);
  //   }
  // };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await updateUserAnswer(q.id, selectedOptions);
      const updatedAnswers = { ...answers, [q.id]: selectedOptions };
      setAnswers(updatedAnswers);
      setShowPopup(false);
    } catch (e) {
      console.error("Error submitting answer:", e);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = async () => {
    setIsSubmitting(true);
    try {
      await updateUserAnswer(q.id, null);
      const updatedAnswers = { ...answers, [q.id]: null };
      setAnswers(updatedAnswers);
      setShowPopup(false);
    } catch (e) {
      console.error("Error skipping question:", e);
    } finally {
      setIsSubmitting(false);
    }
  };

  // const handleSubmit = async () => {
  //   await updateUserAnswer(q.id, selectedOptions);
  //   const updatedAnswers = { ...answers, [q.id]: selectedOptions };
  //   setAnswers(updatedAnswers);
  //   setShowPopup(false);
  // };

  // const handleSkip = async () => {
  //   await updateUserAnswer(q.id, null);
  //   const updatedAnswers = { ...answers, [q.id]: null };
  //   setAnswers(updatedAnswers);
  //   setShowPopup(false);
  // };

  const handleClose = () => {
    if (!q.isMandatory) {
      handleSkip();
    }
  };

  const whiteSpinner = (
    <LoadingOutlined style={{ fontSize: 20, color: "#ffffff" }} spin />
  );

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white rounded-xl shadow-2xl p-6 w-full sm:max-w-[400px] max-w-[350px] relative">
        {!q.isMandatory && (
          <button
            onClick={handleClose}
            className="absolute top-3 right-3 text-[#959698]"
          >
            <IoMdClose className="w-5 h-5" />
          </button>
        )}

        <h2 className="my-3 text-[#959698] font-medium text-[20px] text-center">
          We’d Love to Know
        </h2>
        <div className="mb-4 font-normal text-base text-center text-black">
          {q?.questions?.en || "Question"}
        </div>

        {q.isMultipleSelection && (
          <div className="mb-3 text-sm text-[#959698] font-normal ">
            Select all that apply:
          </div>
        )}

        <div className="flex flex-col gap-3 max-h-[280px] overflow-y-auto scrollbar-hide">
          {q?.options?.map((opt: any) => {
            const selected = isSelected(opt.id);
            return (
              <button
                key={opt.id}
                onClick={() => toggleOption(opt.id)}
                className={`py-2 px-4 rounded-lg border text-left text-[15px] transition relative flex items-center gap-2  ${
                  selected
                    ? "border-primary text-primary"
                    : "border-gray-300 bg-white hover:text-primary hover:border-primary text-[#6B7280]"
                }`}
              >
                {/* {selected && <span>✅</span>} */}
                {opt.option?.en}
              </button>
            );
          })}
        </div>

        <button
          onClick={handleSubmit}
          disabled={isSubmitting || selectedOptions.length === 0}
          className={`mt-6 w-full py-2 px-4 text-white rounded-lg font-semibold transition ${
            isSubmitting || selectedOptions.length === 0
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-orange-500 hover:bg-orange-600"
          }`}
        >
          {/* Submit */}
          {isSubmitting ? <Spin indicator={whiteSpinner} /> : "Submit"}
        </button>
      </div>
    </div>
  );
};

export default QuestionPopup;
